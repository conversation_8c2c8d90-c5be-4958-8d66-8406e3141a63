// Unreviewed Active Orders interfaces
export interface UnreviewedActiveOrder {
  id: number;
  templateType: number;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  account: string;
  tags: string[];
  reward: {
    type: string;
    rewardId: number;
    amount: number;
  }[];
  createdAt: number;
}

export interface UnreviewedActiveOrderSearchParams {
  templateType?: number;
  account?: string;
  page: number;
  limit: number;
}
