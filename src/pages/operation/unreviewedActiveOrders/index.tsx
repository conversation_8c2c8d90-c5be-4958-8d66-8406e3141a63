import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import AutoRefreshControl from '@/components/AutoRefreshControl';
import OperatorCell from '@/components/cells/OperatorCell';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { UnreviewedActiveOrder, UnreviewedActiveOrderSearchParams } from '@/types/activity';
import { cleanSearchParams } from '@/utils/object';

import useUnreviewedActiveOrders from './hooks/useUnreviewedActiveOrders';

type SearchFormValues = Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>;

const SearchFormWrap = ({
  onSearch,
  onReset,
  onRefresh
}: {
  onSearch: (values: Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
  onRefresh: () => void;
}) => {
  const { t } = useTranslation();

  const templateTypeOptions = useMemo(() => {
    return [
      { label: t('common_all'), value: undefined },
      { label: t('pages_operation_unreviewedActiveOrders_template_type_1'), value: 1 },
      { label: t('pages_operation_unreviewedActiveOrders_template_type_2'), value: 2 },
      { label: t('pages_operation_unreviewedActiveOrders_template_type_3'), value: 3 }
    ];
  }, [t]);

  const handleSearch = (values: SearchFormValues) => {
    const searchValues = cleanSearchParams(values);
    onSearch(searchValues);
  };

  return (
    <div className="space-y-4">
      <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
        <div className="flex items-center gap-3 mr-4">
          <div>{t('common_autoRefresh')}:</div>
          <AutoRefreshControl
            onRefresh={onRefresh}
            enabled={true}
            interval={30000}
            isShowManualRefresh={true}
          />
        </div>
        <RForm.Item
          name="templateType"
          label={t('pages_operation_unreviewedActiveOrders_template_type')}
        >
          <RSelect
            placeholder={t('common_please_select', {
              name: t('pages_operation_unreviewedActiveOrders_template_type')
            })}
            options={templateTypeOptions}
            allowClear
          />
        </RForm.Item>
        <RForm.Item name="account" label={t('pages_operation_unreviewedActiveOrders_account')}>
          <RInput
            placeholder={t('common_please_enter', {
              name: t('common_account')
            })}
          />
        </RForm.Item>
      </SearchForm>
    </div>
  );
};

const UnreviewedActiveOrdersPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>>(
    {}
  );

  const formatRewards = (rewards: UnreviewedActiveOrder['reward']) => {
    if (!rewards || rewards.length === 0) return '-';
    return rewards
      .map((reward) => `${reward.type}: ${reward.amount} (ID: ${reward.rewardId})`)
      .join(', ');
  };

  const formatTags = (tags: string[]) => {
    if (!tags || tags.length === 0) return '-';
    return tags.join(', ');
  };

  const getLocalizedContent = (contentMapping: UnreviewedActiveOrder['contentMapping']) => {
    // Try to get zh_tw content first, fallback to first available locale
    const zhTwContent = contentMapping?.zh_tw;
    if (zhTwContent) {
      return zhTwContent.title || zhTwContent.content || '-';
    }

    const firstLocale = Object.keys(contentMapping || {})[0];
    if (firstLocale && contentMapping?.[firstLocale]) {
      return contentMapping[firstLocale].title || contentMapping[firstLocale].content || '-';
    }

    return '-';
  };

  const tableColumns = useMemo(
    () => [
      {
        title: t('common_id'),
        dataIndex: 'id',
        key: 'id'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_template_type'),
        dataIndex: 'templateType',
        key: 'templateType'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_content'),
        dataIndex: 'contentMapping',
        key: 'contentMapping',
        width: 200,
        ellipsis: true,
        render: (contentMapping: UnreviewedActiveOrder['contentMapping']) => {
          const content = getLocalizedContent(contentMapping);
          return <div className="line-clamp-2">{content}</div>;
        }
      },
      {
        title: t('common_account'),
        dataIndex: 'account',
        key: 'account'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_tags'),
        dataIndex: 'tags',
        key: 'tags',
        width: 150,
        ellipsis: true,
        render: (tags: string[]) => {
          const formattedTags = formatTags(tags);
          return <div className="line-clamp-2">{formattedTags}</div>;
        }
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_rewards'),
        dataIndex: 'reward',
        key: 'reward',
        width: 200,
        ellipsis: true,
        render: (rewards: UnreviewedActiveOrder['reward']) => {
          const formattedRewards = formatRewards(rewards);
          return <div className="line-clamp-2">{formattedRewards}</div>;
        }
      },
      {
        title: t('common_addTime'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (createdAt: number) => {
          return <OperatorCell record={{ updatedBy: '', updatedAt: createdAt }} />;
        }
      }
    ],
    [t]
  );

  const unreviewedActiveOrdersQuery = useUnreviewedActiveOrders({
    page,
    limit,
    ...params
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (newParams: Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>) => {
    setParams((oldParams) => {
      // @ts-ignore
      const isTheSame = Object.entries(newParams).every(([key, value]) => oldParams[key] === value);
      if (isTheSame) {
        setTimeout(() => {
          unreviewedActiveOrdersQuery.refetch();
        }, 100);
      }
      return newParams;
    });
    setPage(1);
  };

  const handleReset = () => {
    const resetParams = {};
    setParams(resetParams);
    setPage(1);
  };

  const handleRefresh = () => {
    unreviewedActiveOrdersQuery.refetch();
  };

  // Render table view
  return (
    <TableSearchLayout
      searchFields={
        <SearchFormWrap onSearch={handleSearch} onReset={handleReset} onRefresh={handleRefresh} />
      }
    >
      <RTable
        loading={unreviewedActiveOrdersQuery.isPending}
        rowKey="id"
        dataSource={unreviewedActiveOrdersQuery.data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: unreviewedActiveOrdersQuery.data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default UnreviewedActiveOrdersPage;
