import { Spin } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import OperatorCell from '@/components/cells/OperatorCell';
import RTable from '@/components/RTable';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { UnreviewedActiveOrder } from '@/types/activity';

import useUnreviewedActiveOrders from './hooks/useUnreviewedActiveOrders';

const TableWrap = ({
  list,
  isLoadingList,
  total,
  page,
  limit,
  onPageChange,
  onPageSizeChange
}: {
  list?: UnreviewedActiveOrder[];
  isLoadingList?: boolean;
  total?: number;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (limit: number) => void;
}) => {
  const { t } = useTranslation();

  const formatRewards = (rewards: UnreviewedActiveOrder['reward']) => {
    if (!rewards || rewards.length === 0) return '-';
    return rewards.map(reward => 
      `${reward.type}: ${reward.amount} (ID: ${reward.rewardId})`
    ).join(', ');
  };

  const formatTags = (tags: string[]) => {
    if (!tags || tags.length === 0) return '-';
    return tags.join(', ');
  };

  const getLocalizedContent = (contentMapping: UnreviewedActiveOrder['contentMapping']) => {
    // Try to get zh_tw content first, fallback to first available locale
    const zhTwContent = contentMapping?.zh_tw;
    if (zhTwContent) {
      return zhTwContent.title || zhTwContent.content || '-';
    }
    
    const firstLocale = Object.keys(contentMapping || {})[0];
    if (firstLocale && contentMapping?.[firstLocale]) {
      return contentMapping[firstLocale].title || contentMapping[firstLocale].content || '-';
    }
    
    return '-';
  };

  const tableColumns = useMemo(
    () => [
      {
        title: t('common_id'),
        dataIndex: 'id',
        key: 'id'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_template_type'),
        dataIndex: 'templateType',
        key: 'templateType'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_content'),
        dataIndex: 'contentMapping',
        key: 'contentMapping',
        width: 200,
        ellipsis: true,
        render: (contentMapping: UnreviewedActiveOrder['contentMapping']) => {
          const content = getLocalizedContent(contentMapping);
          return <div className="line-clamp-2">{content}</div>;
        }
      },
      {
        title: t('common_account'),
        dataIndex: 'account',
        key: 'account'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_tags'),
        dataIndex: 'tags',
        key: 'tags',
        width: 150,
        ellipsis: true,
        render: (tags: string[]) => {
          const formattedTags = formatTags(tags);
          return <div className="line-clamp-2">{formattedTags}</div>;
        }
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_rewards'),
        dataIndex: 'reward',
        key: 'reward',
        width: 200,
        ellipsis: true,
        render: (rewards: UnreviewedActiveOrder['reward']) => {
          const formattedRewards = formatRewards(rewards);
          return <div className="line-clamp-2">{formattedRewards}</div>;
        }
      },
      {
        title: t('common_addTime'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (createdAt: number) => {
          return (
            <OperatorCell record={{ updatedBy: '', updatedAt: createdAt }} />
          );
        }
      }
    ],
    [t]
  );

  return (
    <RTable
      loading={isLoadingList}
      rowKey="id"
      dataSource={list || []}
      columns={tableColumns}
      pagination={{
        current: page,
        pageSize: limit,
        total: total || 0,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: onPageChange,
        onShowSizeChange: (current, size) => onPageSizeChange(size)
      }}
    />
  );
};

const UnreviewedActiveOrdersPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});

  const unreviewedActiveOrdersQuery = useUnreviewedActiveOrders({
    page,
    limit
  });

  // Render table view
  return (
    <TableSearchLayout>
      <div className="mb-4">
        <h2 className="text-lg font-bold">{t('router_operation_unreviewedActiveOrders')}</h2>
      </div>
      {unreviewedActiveOrdersQuery.isLoading ? (
        <Spin spinning={true} />
      ) : (
        <TableWrap
          list={unreviewedActiveOrdersQuery.data?.data?.data}
          isLoadingList={unreviewedActiveOrdersQuery.isPending}
          total={unreviewedActiveOrdersQuery.data?.data?.total}
          page={page}
          limit={limit}
          onPageChange={setPage}
          onPageSizeChange={setLimit}
        />
      )}
    </TableSearchLayout>
  );
};

export default UnreviewedActiveOrdersPage;
