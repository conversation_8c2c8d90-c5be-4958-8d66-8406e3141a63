import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import LanguageSelectTab from '@/components/LanguageSelectTab';
import RModal from '@/components/RModal';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';

type ContentDisplayModalProps = {
  open: boolean;
  onClose: () => void;
  isLoadingContent?: boolean;
  contentMapping?: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
};

const ContentDisplayModal = ({ open, onClose, contentMapping, isLoadingContent }: ContentDisplayModalProps) => {
  const { t } = useTranslation();
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const [activeLanguage, setActiveLanguage] = useState('');

  // Get available languages from content mapping
  const availableLanguages = useMemo(
    () => (contentMapping ? Object.keys(contentMapping) : []),
    [contentMapping]
  );

  // Set default active language when modal opens
  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
  };

  // Get content for active language or default
  const getDisplayContent = () => {
    if (!contentMapping) return { title: '', content: '' };

    const targetLanguage = activeLanguage || defaultFrontendLanguage;
    return contentMapping[targetLanguage] || Object.values(contentMapping)[0];
  };

  const displayContent = getDisplayContent();

  // Initialize active language when content changes
  useEffect(() => {
    if (contentMapping && availableLanguages.length > 0) {
      const initialLanguage = availableLanguages.includes(defaultFrontendLanguage)
        ? defaultFrontendLanguage
        : availableLanguages[0];
      setActiveLanguage(initialLanguage);
    }
  }, [defaultFrontendLanguage, availableLanguages, contentMapping]);

  return (
    <RModal
      title={t('pages_frontContent_content')}
      open={open}
      onCancel={onClose}
      width={800}
      okButtonProps={{ show: false }}
      cancelButtonProps={{ text: t('common_close'), show: true }}
      loading={isLoadingContent}
    >
      {contentMapping && (
        <div className="flex flex-col gap-4">
          {/* Language Selection */}
          {availableLanguages.length && (
            <LanguageSelectTab
              languageList={availableLanguages}
              onChange={() => {}} // Read-only, no need to change the list
              activeLanguage={activeLanguage}
              setActiveLanguage={handleLanguageChange}
              isReadOnly={true}
            />
          )}

          {/* Content Display */}
          <div className="space-y-4">
            {/* Title */}
            <div>
              <p className="text-gray-400 mb-2">{t('pages_frontContent_title')}</p>
              <div className="">{displayContent.title}</div>
            </div>

            {/* Content */}
            <div>
              <p className="text-gray-400 mb-2">{t('pages_frontContent_content')}</p>
              <div className="" dangerouslySetInnerHTML={{ __html: displayContent.content }} />
            </div>
          </div>
        </div>
      )}
    </RModal>
  );
};

export default ContentDisplayModal;
