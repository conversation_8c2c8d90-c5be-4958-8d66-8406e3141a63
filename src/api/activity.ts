import { UnreviewedActiveOrder, UnreviewedActiveOrderSearchParams } from '@/types/activity';

import apiRequest, { IResponseDataPage } from './services';

// Unreviewed Active Orders API functions
export const getUnreviewedActiveOrderList = async (params: UnreviewedActiveOrderSearchParams) => {
  return apiRequest().get<IResponseDataPage<UnreviewedActiveOrder>>(
    '/system/activity/list/unreviewed',
    {
      params
    }
  );
};
